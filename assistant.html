<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - AI Assistant</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="/css/pochampally-patterns.css">
    <script src="/js/assistant-initializer.js"></script>
    <style>
        /* Custom styles - Updated to match Pochampally pattern style */
        .bg-primary { background-color: #00695C; }
        .bg-primary-light { background-color: #E0F2F1; }
        .bg-primary-dark { background-color: #004D40; }
        .text-primary { color: #00695C; }
        .bg-accent1 { background-color: #D81B60; }
        .bg-accent1-light { background-color: #FCE4EC; }
        .bg-accent2 { background-color: #FFC107; }
        .bg-accent2-light { background-color: #FFF8E1; }
        .bg-accent3 { background-color: #FF5722; }
        .bg-accent3-light { background-color: #FBE9E7; }
        .bg-accent4 { background-color: #5E35B1; }
        .bg-accent4-light { background-color: #EDE7F6; }

        /* Border colors */
        .border-primary { border-color: #00695C; }

        /* Pochampally pattern overlay */
        .pochampally-overlay {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2300695C' fill-opacity='0.15'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        /* Chat container */
        .chat-container {
            height: calc(100vh - 180px);
        }

        /* Module tabs */
        .module-tab.active {
            background-color: #E0F2F1;
            color: #00695C;
            border-color: #00695C;
        }

        /* Typing indicator */
        .typing-indicator {
            display: flex;
            align-items: center;
        }

        .typing-indicator span {
            height: 8px;
            width: 8px;
            margin: 0 1px;
            background-color: #00695C;
            border-radius: 50%;
            display: inline-block;
            opacity: 0.4;
        }

        .typing-indicator span:nth-child(1) {
            animation: pulse 1s infinite;
        }

        .typing-indicator span:nth-child(2) {
            animation: pulse 1s infinite 0.2s;
        }

        .typing-indicator span:nth-child(3) {
            animation: pulse 1s infinite 0.4s;
        }

        @keyframes pulse {
            0% { opacity: 0.4; }
            50% { opacity: 1; }
            100% { opacity: 0.4; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html">
                    <img src="imgs/logo.png" alt="Shakti Margam Logo" class="h-12 mr-3">
                </a>
                <div>
                    <h1 class="text-2xl font-bold text-primary">Shakti Margam</h1>
                    <p class="text-sm text-gray-500">Empowering Women Entrepreneurs in Telangana</p>
                </div>
            </div>
            <nav class="hidden md:flex space-x-8">
                <a href="index.html" class="text-gray-700 hover:text-primary font-medium">Home</a>
                <a href="assistant.html" class="text-primary font-medium border-b-2 border-primary">Assistant</a>
                <a href="resources.html" class="text-gray-700 hover:text-primary font-medium">Resources</a>
                <a href="about.html" class="text-gray-700 hover:text-primary font-medium">About</a>
            </nav>
            <div class="flex items-center">
                <div class="mr-4 text-right hidden md:block">
                    <p class="text-sm font-medium">Welcome</p>
                    <p class="text-xs text-gray-500">Guest User</p>
                </div>
                <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors duration-300">
                    Sign In
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="grid grid-cols-1 md:grid-cols-4">
                <!-- Sidebar -->
                <div class="bg-gray-50 p-4 border-r border-gray-200">
                    <h2 class="text-lg font-semibold mb-4">Modules</h2>
                    <div class="space-y-2">
                        <button class="module-tab active w-full text-left px-4 py-2 rounded-lg border border-transparent hover:bg-primary-light hover:text-primary transition-colors duration-200">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                                    <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                                </svg>
                                <span>Business Assessment</span>
                            </div>
                        </button>
                        <button class="module-tab w-full text-left px-4 py-2 rounded-lg border border-transparent hover:bg-primary-light hover:text-primary transition-colors duration-200">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                </svg>
                                <span>Social Media Strategy</span>
                            </div>
                        </button>
                        <button class="module-tab w-full text-left px-4 py-2 rounded-lg border border-transparent hover:bg-primary-light hover:text-primary transition-colors duration-200">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                                <span>Financial Advisor</span>
                            </div>
                        </button>
                        <button class="module-tab w-full text-left px-4 py-2 rounded-lg border border-transparent hover:bg-primary-light hover:text-primary transition-colors duration-200">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path>
                                </svg>
                                <span>Customer Profiling</span>
                            </div>
                        </button>
                        <button class="module-tab w-full text-left px-4 py-2 rounded-lg border border-transparent hover:bg-primary-light hover:text-primary transition-colors duration-200">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                </svg>
                                <span>Government Schemes</span>
                            </div>
                        </button>
                    </div>

                    <h2 class="text-lg font-semibold mt-8 mb-4">Business Profile</h2>
                    <div class="bg-white p-4 rounded-lg border border-gray-200">
                        <p class="text-sm text-gray-600 mb-4">Complete your business profile to get personalized recommendations.</p>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-primary h-2.5 rounded-full" style="width: 25%"></div>
                        </div>
                        <p class="text-xs text-gray-500 mt-2">25% Complete</p>
                        <button class="mt-4 w-full bg-primary-light text-primary px-4 py-2 rounded-lg hover:bg-primary hover:text-white transition-colors duration-300">
                            Complete Profile
                        </button>
                    </div>
                </div>

                <!-- Chat Area -->
                <div class="col-span-3 flex flex-col">
                    <!-- Chat Header -->
                    <div class="bg-primary-light p-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <div>
                                <h2 class="text-lg font-semibold text-primary">Business Assessment</h2>
                                <p class="text-sm text-gray-600">Analyze your business idea and current performance</p>
                            </div>
                            <div>
                                <button class="bg-white text-primary px-4 py-2 rounded-lg border border-primary hover:bg-primary hover:text-white transition-colors duration-300">
                                    <div class="flex items-center">
                                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                        </svg>
                                        <span>Export Insights</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div class="flex-grow p-4 overflow-y-auto chat-container" id="chat-messages">
                        <!-- Welcome Message -->
                        <div class="mb-4">
                            <div class="bg-primary-light text-gray-800 p-4 rounded-lg rounded-tl-none max-w-3xl">
                                <h3 class="font-semibold text-primary mb-2">Welcome to Shakti Margam AI Assistant!</h3>
                                <p class="mb-3">
                                    I'm here to help you with your business journey. Let's start by understanding your business better.
                                </p>
                                <p class="mb-3">
                                    You can ask me questions about:
                                </p>
                                <ul class="list-disc pl-5 mb-3 space-y-1">
                                    <li>Business planning and strategy</li>
                                    <li>Market analysis for Telangana region</li>
                                    <li>Financial planning and funding options</li>
                                    <li>Social media and marketing strategies</li>
                                    <li>Government schemes for women entrepreneurs</li>
                                </ul>
                                <p>
                                    What would you like to discuss today?
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Input -->
                    <div class="p-4 border-t border-gray-200">
                        <div class="flex items-center">
                            <input type="text" id="chat-input" placeholder="Type your message here..." class="flex-grow px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            <button id="send-button" class="bg-primary text-white px-4 py-2 rounded-r-lg hover:bg-primary-dark transition-colors duration-300">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="flex justify-between mt-2 text-xs text-gray-500">
                            <div>
                                <span>Powered by ShaktiMargamAIEngine</span>
                            </div>
                            <div>
                                <button id="clear-conversation" class="text-primary hover:underline">Clear conversation</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chat-input');
            const sendButton = document.getElementById('send-button');
            const chatMessages = document.getElementById('chat-messages');
            const moduleTabs = document.querySelectorAll('.module-tab');

            // Handle module tab switching
            moduleTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    moduleTabs.forEach(t => t.classList.remove('active'));
                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Update the module header
                    const moduleText = this.querySelector('span').textContent.trim();
                    const moduleHeader = document.querySelector('.bg-primary-light h2');
                    if (moduleHeader) {
                        moduleHeader.textContent = moduleText;
                    }

                    // Map the module text to module IDs
                    const moduleMap = {
                        'Business Assessment': 'initial-assessment',
                        'Social Media Strategy': 'social-media-strategy',
                        'Financial Advisor': 'financial-analysis',
                        'Customer Profiling': 'customer-profiling',
                        'Government Schemes': 'telangana-market-insights'
                    };

                    const moduleId = moduleMap[moduleText] || 'initial-assessment';

                    // Store the selected module in localStorage
                    localStorage.setItem('shaktiMargamActiveModule', moduleId);

                    // If we have a global ShaktiMargam object, update the active module
                    if (window.ShaktiMargam && window.ShaktiMargam.setActiveModule) {
                        window.ShaktiMargam.setActiveModule(moduleId);
                    }

                    // Update the module description
                    const moduleDescription = document.querySelector('.bg-primary-light p');
                    if (moduleDescription) {
                        const descriptions = {
                            'Business Assessment': 'Analyze your business idea and current performance',
                            'Social Media Strategy': 'Create effective social media plans for your business',
                            'Financial Advisor': 'Get guidance on cash flow and funding opportunities',
                            'Customer Profiling': 'Develop detailed customer personas for your business',
                            'Government Schemes': 'Identify government programs you\'re eligible for'
                        };

                        moduleDescription.textContent = descriptions[moduleText] || '';
                    }
                });
            });

            // Function to add a message to the chat
            function addMessage(message, isUser = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'mb-4';

                if (isUser) {
                    messageDiv.innerHTML = `
                        <div class="bg-accent1-light text-gray-800 p-4 rounded-lg rounded-tr-none max-w-3xl ml-auto">
                            <p>${message}</p>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="bg-primary-light text-gray-800 p-4 rounded-lg rounded-tl-none max-w-3xl">
                            <p>${message}</p>
                        </div>
                    `;
                }

                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Function to show typing indicator
            function showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'mb-4';
                typingDiv.id = 'typing-indicator';
                typingDiv.innerHTML = `
                    <div class="bg-primary-light text-gray-800 p-3 rounded-lg rounded-tl-none max-w-xs">
                        <div class="typing-indicator">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                `;
                chatMessages.appendChild(typingDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Function to remove typing indicator
            function removeTypingIndicator() {
                const typingIndicator = document.getElementById('typing-indicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }

            // Function to handle sending a message
            function sendMessage() {
                const message = chatInput.value.trim();
                if (message === '') return;

                // Add user message to chat
                addMessage(message, true);

                // Clear input
                chatInput.value = '';

                // Show typing indicator
                showTypingIndicator();

                // Get the active module
                const activeTab = document.querySelector('.module-tab.active');
                let moduleId = 'initial-assessment';

                if (activeTab) {
                    const moduleText = activeTab.querySelector('span').textContent.trim();

                    // Map the module text to module IDs
                    const moduleMap = {
                        'Business Assessment': 'initial-assessment',
                        'Social Media Strategy': 'social-media-strategy',
                        'Financial Advisor': 'financial-analysis',
                        'Customer Profiling': 'customer-profiling',
                        'Government Schemes': 'telangana-market-insights'
                    };

                    moduleId = moduleMap[moduleText] || moduleId;
                }

                // Try to use the backend API if available
                try {
                    const apiEndpoint = window.API_ENDPOINT || 'http://localhost:3000/api/ai/message';

                    fetch(apiEndpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer development_key'
                        },
                        body: JSON.stringify({
                            message: message,
                            activeModule: moduleId
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('API response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        removeTypingIndicator();
                        addMessage(data.response);
                    })
                    .catch(error => {
                        console.error('Error calling AI API:', error);
                        // Fall back to demo responses
                        fallbackToDemo(message);
                    });
                } catch (error) {
                    console.error('Error setting up API call:', error);
                    // Fall back to demo responses
                    fallbackToDemo(message);
                }
            }

            // Fallback to demo responses when API is not available
            function fallbackToDemo(message) {
                setTimeout(() => {
                    removeTypingIndicator();

                    // Generate response based on message content
                    let response = '';
                    const lowerMessage = message.toLowerCase();

                    if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
                        response = "Hello! I'm your Shakti Margam AI assistant. How can I help your business today?";
                    } else if (lowerMessage.includes('business') && lowerMessage.includes('plan')) {
                        response = "Creating a business plan is crucial. Start with your mission, target market analysis, and financial projections. Would you like me to help you with a specific section?";
                    } else if (lowerMessage.includes('funding') || lowerMessage.includes('loan')) {
                        response = "There are several funding options for women entrepreneurs in Telangana, including WE-HUB grants, MUDRA loans, and Stand-Up India scheme. Would you like details on any specific program?";
                    } else if (lowerMessage.includes('marketing') || lowerMessage.includes('social media')) {
                        response = "For effective marketing, focus on identifying your target audience and creating content that resonates with them. Social media platforms like Instagram and Facebook are great for showcasing products.";
                    } else if (lowerMessage.includes('government') || lowerMessage.includes('scheme')) {
                        response = "The Telangana government offers several schemes for women entrepreneurs, including T-PRIDE, TSWEIS, and WE-HUB initiatives. These provide financial assistance, training, and mentorship. Would you like me to explain any specific scheme in detail?";
                    } else {
                        response = "That's a great question. I'd recommend focusing on your unique strengths as an entrepreneur and identifying market gaps. Would you like more specific guidance on this topic?";
                    }

                    addMessage(response);
                }, 1500);
            }

            // Function to clear the conversation
            function clearConversation() {
                // Clear the chat messages except for the welcome message
                while (chatMessages.children.length > 1) {
                    chatMessages.removeChild(chatMessages.lastChild);
                }

                // Clear localStorage
                localStorage.removeItem('shaktiMargamConversation');

                // If we have a global ShaktiMargam object, clear the conversation
                if (window.ShaktiMargam && window.ShaktiMargam.clearConversation) {
                    window.ShaktiMargam.clearConversation();
                }
            }

            // Event listeners
            sendButton.addEventListener('click', sendMessage);

            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Clear conversation button
            const clearButton = document.getElementById('clear-conversation');
            if (clearButton) {
                clearButton.addEventListener('click', clearConversation);
            }
        });
    </script>
</body>
</html>
