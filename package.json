{"name": "shakti-margam", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:prod": "vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@pinecone-database/pinecone": "^6.0.0", "@supabase/supabase-js": "^2.49.5", "@types/react-transition-group": "^4.4.12", "dotenv": "^16.5.0", "framer-motion": "^12.12.1", "openai": "^4.100.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.0", "react-transition-group": "^4.4.5", "recharts": "^2.15.3"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.21", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.0.2", "vite": "^4.4.5"}}