/* Reset any potential global styles that might affect the splash container */
html, body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  height: 100%;
  width: 100%;
}

.splash-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%; /* Use 100% instead of 100vw to avoid scrollbar issues */
  height: 100%; /* Use 100% instead of 100vh to avoid scrollbar issues */
  background: linear-gradient(135deg, #f8eeff 0%, #f1e4f3 50%, #e8f4f2 100%); /* Soft gradient using Pochampally-inspired colors */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Ensures it's above all other elements */
  opacity: 1; /* Make it visible immediately */
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  overflow: hidden;
  /* Force the container to take up the entire viewport */
  min-width: 100%;
  min-height: 100%;
}

@keyframes fadeInSplash {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.splash-container.fade-out {
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.splash-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 90%;
  max-width: 600px;
  padding: 3rem;
  backdrop-filter: blur(10px);
  background: linear-gradient(135deg, #f8eeff 0%, #f1e4f3 50%, #e8f4f2 100%);
  margin: 0 auto;
  position: relative;
  z-index: 10;
  overflow: visible;
}

.logo-container {
  position: relative;
  margin-bottom: 2.5rem; /* Reduced spacing */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.shakti-logo {
  width: 180px; /* Increased logo size */
  height: auto;
  opacity: 1; /* Make it visible immediately */
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  object-fit: contain;
  margin-bottom: 0; /* Remove bottom margin */
}

.wehub-badge {
  position: relative; /* Changed from absolute to relative */
  margin-top: 1rem; /* Add margin instead of absolute positioning */
  background-color: #E84A5F; /* Explicitly using the Pochampally accent1 color */
  color: white;
  font-size: 0.8rem;
  padding: 0.4rem 1.2rem;
  border-radius: 30px;
  font-weight: 700;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  opacity: 1; /* Make it visible immediately */
  z-index: 20;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  border: none;
  text-align: center;
  width: fit-content;
}

.splash-text h1 {
  font-size: 3.2rem;
  color: #6A2C70 !important; /* Explicitly using the Pochampally primary color */
  margin-bottom: 0.5rem;
  opacity: 1; /* Make it visible immediately */
  font-weight: 700;
  letter-spacing: -0.5px;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.splash-text h2 {
  font-size: 1.6rem;
  color: #E84A5F !important; /* Explicitly using the Pochampally accent1 color */
  margin-bottom: 1.2rem;
  opacity: 1; /* Make it visible immediately */
  font-weight: 600;
  text-align: center;
}

.splash-text p {
  font-size: 1rem;
  color: #333; /* Slightly darker for better contrast */
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 1; /* Make it visible immediately */
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  font-weight: 500; /* Slightly bolder for better readability */
}

.animation-container {
  position: relative;
  width: 100%;
  height: 10px;
  margin-top: 1.5rem;
  overflow: visible;
}

.pochampally-pattern {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: linear-gradient(90deg,
    #6A2C70 0%,
    #E84A5F 25%,
    #2A9D8F 50%,
    #F9C74F 75%,
    #577590 100%
  );
  opacity: 1; /* Make it visible immediately */
  border-radius: 3px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.skip-button {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  background-color: transparent;
  border: 1px solid #6A2C70;
  color: #6A2C70;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 1.2rem;
  border-radius: 30px;
  transition: all 0.3s ease;
  box-shadow: none;
  z-index: 10000; /* Ensures it's above the splash container */
  opacity: 1; /* Make it visible immediately */
  pointer-events: auto;
}

.skip-button:hover {
  background-color: #6A2C70;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.skip-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(106, 44, 112, 0.2);
}

/* Animations removed since we're making everything visible immediately */

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .splash-content {
    max-width: 550px;
  }
}

@media (max-width: 992px) {
  .splash-content {
    max-width: 500px;
    padding: 2.5rem;
  }

  .splash-text h1 {
    font-size: 2.8rem;
  }

  .splash-text h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .splash-content {
    max-width: 90%;
    padding: 2rem;
  }

  .splash-text h1 {
    font-size: 2.5rem;
  }

  .splash-text h2 {
    font-size: 1.4rem;
  }

  .shakti-logo {
    width: 150px;
  }

  .wehub-badge {
    margin-top: 0.8rem;
    font-size: 0.75rem;
    padding: 0.4rem 1rem;
  }
}

@media (max-width: 576px) {
  .splash-content {
    padding: 1.5rem;
    width: 92%;
  }

  .splash-text h1 {
    font-size: 2rem;
  }

  .splash-text h2 {
    font-size: 1.2rem;
  }

  .splash-text p {
    font-size: 0.9rem;
  }

  .shakti-logo {
    width: 120px;
  }

  .wehub-badge {
    margin-top: 0.7rem;
    font-size: 0.7rem;
    padding: 0.3rem 0.8rem;
  }

  .animation-container {
    margin-top: 1rem;
  }

  .skip-button {
    bottom: 1rem;
    right: 1rem;
    font-size: 0.8rem;
    padding: 0.4rem 0.9rem;
  }
}

/* Add a subtle pochampally pattern background to the splash container */
.splash-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236A2C70' fill-opacity='0.06'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-repeat: repeat;
  background-position: center center;
  opacity: 0.8;
  z-index: -1;
}
