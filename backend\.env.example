# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration (Legacy - will be replaced by <PERSON><PERSON><PERSON> Auth)
JWT_SECRET=shakti_margam_secret_key
JWT_EXPIRES_IN=30d

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# PostgreSQL Configuration (Legacy - will be replaced by Supabase)
PG_HOST=localhost
PG_PORT=5432
PG_DATABASE=shakti_margam
PG_USER=postgres
PG_PASSWORD=postgres

# MongoDB Configuration (For vector embeddings backup)
MONGO_URI=mongodb://localhost:27017/shakti_margam

# LLM Configuration
OPENAI_API_KEY=your_openai_api_key
LLM_PROVIDER=openai
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_COMPLETION_MODEL=gpt-4-turbo-preview
ENABLE_LLM_CACHE=true
LLM_CACHE_TTL=3600000

# Vector Store Configuration
VECTOR_STORE_PROVIDER=pinecone
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=shakti-margam-index
DEFAULT_TOP_K=5
ENABLE_RERANKING=true
RERANKING_MODEL=cross-encoder/ms-marco-MiniLM-L-6-v2
ENABLE_HYBRID_SEARCH=true
VECTOR_WEIGHT=0.7
KEYWORD_WEIGHT=0.3

# Context Management
MAX_CONTEXT_SIZE=10
CONTEXT_TTL=3600000

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=Shakti Margam <<EMAIL>>

# WE-HUB API Configuration
WEHUB_API_URL=https://wehub.telangana.gov.in/api
WEHUB_API_KEY=your_wehub_api_key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
