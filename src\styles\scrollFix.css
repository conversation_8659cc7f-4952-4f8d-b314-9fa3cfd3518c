/*
 * Scrolling Fix for Shakti Margam - Highly Simplified Version
 * Focus: Minimal intervention, primarily for splash screen state and scrollbar aesthetics.
 * Let Tailwind and browser defaults handle most layout and overflow.
 */

/* Default scroll behavior for html and body */
html, body {
  overflow-x: hidden; /* Prevent horizontal scrollbars globally */
  /* overflow-y: auto; - Let browser default handle this or Tailwind's overflow classes */
  /* height: 100%; - Avoid forcing height here, can conflict with min-h-screen */
  /* width: 100%; - Generally default, ensure no other global CSS restricts it */
  margin: 0;
  padding: 0;
}

/* Splash screen active state - this is a temporary override, so !important is acceptable */
html.splash-active,
body.splash-active {
  overflow: hidden !important;
  height: 100vh !important; 
  width: 100vw !important; 
}

/*
  The following rules that were previously in scrollFix.css for main, #root, etc.,
  have been removed to prevent conflicts with Tailwind's layout utilities like
  min-h-screen, flex-grow, and component-specific overflow needs.
  Tailwind classes should be the primary method for controlling layout and overflow
  on these elements within your components.
*/

/* Scrollbar Styles - These are generally safe as they style the browser UI */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1; 
}

::-webkit-scrollbar-thumb {
  background: #00695C; /* Pochampally Primary Green for scrollbar */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #004D40; /* Darker Green */
}
