{"name": "shakti-margam-backend", "version": "1.0.0", "description": "Backend API for Shakti Margam AI Assistant", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=src/__tests__/unit", "test:integration": "jest --testPathPattern=src/__tests__/integration", "seed": "node src/scripts/seedDatabase.js", "seed:vector": "node src/scripts/seedVectorStore.js", "migrate": "node src/scripts/migrateToSupabase.js", "build": "node deploy.js --build-only", "deploy:dev": "cross-env DEPLOY_ENV=development node deploy.js", "deploy:staging": "cross-env DEPLOY_ENV=staging node deploy.js", "deploy:prod": "cross-env DEPLOY_ENV=production node deploy.js", "preview": "node src/server.js"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^6.1.5", "jsonwebtoken": "^9.0.0", "mongoose": "^7.1.0", "morgan": "^1.10.0", "nodemailer": "^6.9.1", "openai": "^4.0.0", "pg": "^8.10.0", "pg-hstore": "^2.3.4", "pinecone-client": "^1.1.0", "sequelize": "^6.31.1", "winston": "^3.8.2"}, "devDependencies": {"cross-env": "^7.0.3", "jest": "^29.5.0", "jest-mock-extended": "^3.0.5", "nodemon": "^2.0.22", "supertest": "^6.3.3", "mock-req-res": "^1.2.1"}, "engines": {"node": ">=14.0.0"}}