/* Custom styles for S<PERSON> Margam */

/* Pochampally Ikat Pattern */
.pattern-pochampally {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236A2C70' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Tangedu Flower Animation */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* Custom Button Styles */
/* .btn-primary {
  @apply px-6 py-3 bg-primary text-white font-medium rounded-lg shadow-lg hover:bg-primary-dark transition-colors duration-300;
} */
/* Use: className="px-6 py-3 bg-primary text-white font-medium rounded-lg shadow-lg hover:bg-primary-dark transition-colors duration-300" in JSX */

/* .btn-secondary {
  @apply px-6 py-3 bg-white text-primary font-medium rounded-lg shadow-lg hover:bg-gray-100 transition-colors duration-300;
} */
/* Use: className="px-6 py-3 bg-white text-primary font-medium rounded-lg shadow-lg hover:bg-gray-100 transition-colors duration-300" in JSX */

/* Card Hover Effects */
.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  /* padding: 2rem 1.5rem; */ /* Commented out: Let Tailwind manage padding in JSX for consistency */
  min-height: 180px; /* This can stay if a minimum height is desired */
  border-radius: 0.75rem; /* Tailwind's rounded-lg is 0.5rem, this is slightly more. Or use rounded-xl (1rem) if preferred */
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Ensure feature card icons are constrained if Tailwind classes are overridden */
.feature-card .icon-wrapper > svg {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}


/* Gradient Text */
/* .gradient-text {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent1;
} */
/* Use: className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent1" in JSX */

/* Custom Scrollbar - These are fine, but already in scrollFix.css with !important */
/* Consider keeping scrollbar styles in one place, e.g., scrollFix.css if they need !important */
/* ::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #6A2C70;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4A1D4E;
} */

/* Responsive Adjustments */
@media (max-width: 768px) {
  .hero-section { /* This class is not used in StaticLandingPage.tsx, hero styling is direct Tailwind */
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
  
  .hero-section h1 {
    font-size: 2.5rem;
  }
  
  .hero-section p {
    font-size: 1.25rem;
  }
}

body, .font-sans {
  /* font-size: 18px; */ /* Let Tailwind's responsive text sizes handle this, or base html font-size */
  line-height: 1.7;
  font-family: 'Inter', 'Noto Sans Telugu', 'Noto Sans Devanagari', sans-serif;
}

.btn-primary, .btn-secondary { /* These are @apply rules, ensure 'primary' color is defined for Tailwind */
  font-size: 1.125rem;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
}
