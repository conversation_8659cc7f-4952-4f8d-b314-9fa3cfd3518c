/* 
 * Shakti Margam Animations
 * This file contains global animations and transitions for the Shakti Margam platform
 */

/* ===== Page Transitions ===== */
.page-transition-enter {
  opacity: 0;
  transform: translateX(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 350ms ease-out, transform 350ms ease-out;
}

.page-transition-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateX(-20px);
  transition: opacity 350ms ease-out, transform 350ms ease-out;
}

/* ===== Button Animations ===== */
@keyframes buttonPulse {
  0% { box-shadow: 0 0 0 0 rgba(106, 44, 112, 0.7); }
  70% { box-shadow: 0 0 0 8px rgba(106, 44, 112, 0); }
  100% { box-shadow: 0 0 0 0 rgba(106, 44, 112, 0); }
}

.btn-primary {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-primary:active {
  transform: scale(0.95);
}

.btn-primary.clicked {
  animation: buttonPulse 0.6s ease;
}

/* ===== Skeleton Loading Animation ===== */
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.skeleton-loader {
  background: linear-gradient(90deg, #f0f0f0 25%, #e8e8e8 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 4px;
  height: 16px;
  margin-bottom: 8px;
}

/* Skeleton loader variations */
.skeleton-loader.title {
  height: 24px;
  width: 70%;
  margin-bottom: 16px;
}

.skeleton-loader.text {
  height: 16px;
  width: 100%;
}

.skeleton-loader.button {
  height: 40px;
  width: 120px;
  border-radius: 20px;
}

.skeleton-loader.circle {
  height: 48px;
  width: 48px;
  border-radius: 50%;
}

.skeleton-loader.card {
  height: 200px;
  border-radius: 8px;
}

/* ===== Feature Card Hover Effects ===== */
.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
  border-bottom: 3px solid transparent;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 3px solid #E84A5F; /* Pochampally accent1 color */
}

/* Alternate accent colors for different cards */
.feature-card:nth-child(2n):hover {
  border-bottom-color: #2A9D8F; /* Pochampally accent2 color */
}

.feature-card:nth-child(3n):hover {
  border-bottom-color: #F9C74F; /* Pochampally accent3 color */
}

.feature-card:nth-child(4n):hover {
  border-bottom-color: #577590; /* Pochampally accent4 color */
}

/* ===== Sidebar Navigation Indicator ===== */
.sidebar-nav {
  position: relative;
}

.nav-indicator {
  position: absolute;
  left: 0;
  height: 3px;
  background-color: #6A2C70;
  border-radius: 3px;
  transition: transform 0.3s ease, width 0.3s ease;
}

/* ===== Form Input Focus States ===== */
.form-group {
  position: relative;
}

.form-control {
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: transparent;
}

.form-control:focus + .input-underline {
  transform: scaleX(1);
  opacity: 1;
}

.input-underline {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #6A2C70, #E84A5F);
  transform: scaleX(0);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
  transform-origin: left;
}

.form-label {
  transition: transform 0.3s ease, color 0.3s ease;
}

.form-control:focus ~ .form-label {
  transform: translateY(-5px) scale(0.9);
  color: #6A2C70;
}

/* ===== Fade In Animation ===== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* ===== Slide Up Animation ===== */
@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.5s ease-out forwards;
}

/* ===== Scale Animation ===== */
@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.95);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

.scale-in {
  animation: scaleIn 0.5s ease-out forwards;
}
