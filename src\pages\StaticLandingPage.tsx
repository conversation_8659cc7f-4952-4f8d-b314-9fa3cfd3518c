import React, { useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
// Ensure CSS import order is: Tailwind base (in index.css) -> other global styles -> component-specific styles
// import '../index.css'; // Should contain Tailwind directives
import '../styles/pochampally-patterns.css'; 
import '../styles/custom.css'; 
import '../styles/scrollFix.css'; // MUST be the simplified version
import SplashRedirect from '../components/SplashRedirect';
// Ensure scrollUtils.ts is the version that ONLY manages classes for splash, not inline styles
import { enableScrolling, resetScroll, applyScrollingFix } from '../utils/scrollUtils';

const StaticLandingPage: React.FC = () => {
  const pageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    resetScroll(); 
    // applyScrollingFix should be a no-op or apply only necessary accessibility classes now
    if (pageRef.current) {
      // applyScrollingFix(pageRef.current); // Generally, prefer CSS/Tailwind for layout
    }

    const timer = setTimeout(() => {
      enableScrolling(); 
    }, 100);

    const handleUserInteraction = () => {
      enableScrolling();
    };
    window.addEventListener('click', handleUserInteraction);
    window.addEventListener('touchstart', handleUserInteraction);
    window.addEventListener('scroll', handleUserInteraction, { passive: true });
    
    return () => {
      clearTimeout(timer);
      window.removeEventListener('click', handleUserInteraction);
      window.removeEventListener('touchstart', handleUserInteraction);
      window.removeEventListener('scroll', handleUserInteraction);
      enableScrolling(); 
    };
  }, []);

  useEffect(() => {
    window.redirectToAssistant = () => { window.location.href = '/assistant'; };
    window.redirectToAssistantWithModule = (moduleId: string) => {
      localStorage.setItem('shaktiMargamActiveModule', moduleId);
      window.location.href = '/assistant';
    };
    window.handleFormSubmit = (event: any, formType: string) => {
      event.preventDefault();
      const form = event.target;
      const formData = new FormData(form);
      const data: any = {};
      for (let [key, value] of formData.entries()) { data[key] = value; }
      data.formType = formType;
      const submitButton = form.querySelector('button[type="submit"]');
      if (submitButton) {
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = '<span class="inline-block animate-spin mr-2">↻</span> Submitting...';
        submitButton.disabled = true;
        setTimeout(() => {
          console.log('Form submitted:', data);
          if (window.showSuccessMessage) { window.showSuccessMessage(form); }
          submitButton.innerHTML = originalButtonText;
          submitButton.disabled = false;
        }, 1000);
      }
      return false;
    };
    window.showSuccessMessage = (form: HTMLFormElement) => {
      const successMessage = document.createElement('div');
      successMessage.className = 'mt-4 p-3 bg-green-100 text-green-700 rounded-lg'; 
      successMessage.textContent = 'Thank you! Your submission has been received.';
      form.appendChild(successMessage);
      form.reset();
      setTimeout(() => { successMessage.remove(); }, 3000);
    };
    window.sendDemoMessage = () => {
      const inputElement = document.getElementById('demo-chat-input') as HTMLInputElement;
      if (!inputElement) return;
      const message = inputElement.value.trim();
      if (!message) return;
      let chatContainer = inputElement.closest('.p-3')?.querySelector('.chat-messages-container-selector'); 
      if (!chatContainer) {
          chatContainer = document.querySelector('.chat-messages-container-selector'); 
          if (!chatContainer) { console.error("Chat container not found with selector: .chat-messages-container-selector"); return; }
      }
      const userMessageDiv = document.createElement('div');
      userMessageDiv.className = 'mb-2';
      userMessageDiv.innerHTML = `<div class="bg-amber-100 text-slate-800 p-2 rounded-lg rounded-tl-none max-w-xs ml-auto text-sm"><p>${message}</p></div>`;
      chatContainer.appendChild(userMessageDiv);
      inputElement.value = '';
      const typingIndicator = document.createElement('div');
      typingIndicator.className = 'mb-2 typing-indicator'; 
      typingIndicator.innerHTML = `<div class="bg-teal-50 text-slate-800 p-3 rounded-lg rounded-tr-none max-w-xs flex items-center"><span class="dot"></span><span class="dot"></span><span class="dot"></span></div>`;
      chatContainer.appendChild(typingIndicator);
      chatContainer.scrollTop = chatContainer.scrollHeight;
      setTimeout(() => {
        if (typingIndicator && typingIndicator.parentNode === chatContainer) { chatContainer.removeChild(typingIndicator); }
        let aiResponse = '';
        const lowerMessage = message.toLowerCase();
        if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) { aiResponse = "Hello! I'm your Shakti Margam AI assistant. How can I help your business today?"; }
        else if (lowerMessage.includes('business') && lowerMessage.includes('plan')) { aiResponse = "Creating a business plan is crucial..."; }
        else if (lowerMessage.includes('funding') || lowerMessage.includes('loan')) { aiResponse = "There are several funding options..."; }
        else if (lowerMessage.includes('marketing') || lowerMessage.includes('social media')) { aiResponse = "For effective marketing, focus on..."; }
        else { aiResponse = "That's a great question..."; }
        const aiMessageDiv = document.createElement('div');
        aiMessageDiv.className = 'mb-2';
        aiMessageDiv.innerHTML = `<div class="bg-teal-50 text-slate-800 p-2 rounded-lg rounded-tr-none max-w-xs text-sm"><p>${aiResponse}</p></div>`;
        chatContainer.appendChild(aiMessageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }, 1000);
    };
    const inputElement = document.getElementById('demo-chat-input');
    if (inputElement) {
      inputElement.addEventListener('keypress', function(e) { if (e.key === 'Enter') { window.sendDemoMessage(); } });
    }
    window.API_ENDPOINT = import.meta.env.VITE_API_ENDPOINT || '';
    return () => {
      const el = document.getElementById('demo-chat-input');
      if (el) { el.removeEventListener('keypress', function(e) { if (e.key === 'Enter') { window.sendDemoMessage(); } });}
    };
  }, []);

  return (
    <div ref={pageRef} className="font-sans flex flex-col min-h-screen w-full bg-slate-900 overflow-x-hidden relative"> {/* Added overflow-x-hidden */}
      <SplashRedirect />

      <style>
        {`
          /* --- Dark Theme Styles (Header Buttons, Hero Section) --- */
          .bg-primary-accent { background-color: #FFB300; } /* Amber */
          .text-primary-accent-contrast { color: #2F2000; } 
          .border-primary-accent { border-color: #FFB300; }
          .hover-bg-primary-accent-dark:hover { background-color: #E69500; }

          .bg-hero-teal { background-color: #004D40; } 
          .text-hero-light { color: #E0F2F1; } 
          .text-hero-white { color: #FFFFFF; }

          .dark-hero-pochampally-overlay {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23E0F2F1' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
          }

          /* --- Light Theme (Pochampally) Styles (Sections AFTER Hero) --- */
          .pochampally-bg-primary { background-color: #00695C; } 
          .pochampally-bg-primary-light { background-color: #E0F2F1; }
          .pochampally-bg-primary-dark { background-color: #004D40; }
          .pochampally-text-primary { color: #00695C; }
          .pochampally-border-primary { border-color: #00695C; }
          .hover-pochampally-bg-primary-dark:hover { background-color: #004D40; }
          .hover-pochampally-bg-primary-light:hover { background-color: #E0F2F1; }

          .pochampally-bg-accent1 { background-color: #D81B60; } 
          .pochampally-bg-accent1-light { background-color: #FCE4EC; }
          .pochampally-text-accent1 { color: #D81B60; }
          .pochampally-text-accent1-darker { color: #8C103E; } 

          .pochampally-bg-accent2 { background-color: #FFC107; } 
          .pochampally-bg-accent2-light { background-color: #FFF8E1; }
          .pochampally-text-accent2 { color: #FFC107; }
          .pochampally-text-accent2-darker { color: #B28000; } 

          .pochampally-bg-accent3 { background-color: #FF5722; } 
          .pochampally-bg-accent3-light { background-color: #FBE9E7; }
          .pochampally-text-accent3 { color: #FF5722; }
          .pochampally-text-accent3-darker { color: #C33A18; } 

          .pochampally-bg-accent4 { background-color: #5E35B1; } 
          .pochampally-bg-accent4-light { background-color: #EDE7F6; }
          .pochampally-text-accent4 { color: #5E35B1; }
          .pochampally-text-accent4-darker { color: #3C2271; } 
          
          .pochampally-bg-gradient-hero { background: linear-gradient(to bottom, #E0F2F1, #FFFFFF); }
          .pochampally-bg-gradient-cta { background: linear-gradient(to right, #00695C, #D81B60); }

          .pochampally-overlay-light-theme {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2300695C' fill-opacity='0.15'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
          }

          /* --- Common Styles --- */
          @keyframes float { 0% { transform: translateY(0px); } 50% { transform: translateY(-10px); } 100% { transform: translateY(0px); } }
          .float-animation { animation: float 6s ease-in-out infinite; }
          
          .feature-card { transition: transform 0.3s ease, box-shadow 0.3s ease; display: flex; flex-direction: column; height: 100%;}
          .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
          }
          
          .typing-indicator .dot {
              background-color: #FFB300; 
              display: inline-block; width: 8px; height: 8px; border-radius: 50%;
              margin-right: 4px; animation: typing 1.4s infinite ease-in-out both;
          }
          .typing-indicator .dot:nth-child(1) { animation-delay: 0s; }
          .typing-indicator .dot:nth-child(2) { animation-delay: 0.2s; }
          .typing-indicator .dot:nth-child(3) { animation-delay: 0.4s; }
          @keyframes typing { 0%, 80%, 100% { transform: scale(0.6); opacity: 0.6; } 40% { transform: scale(1); opacity: 1; } }
        `}
      </style>

      <header className="bg-white shadow-md sticky top-0 z-50 w-full">
        <div className="container mx-auto px-6">
            <div className="flex items-center justify-between h-16 md:h-20">
                <div className="flex items-center flex-1 min-w-0 gap-3 sm:gap-4"> {/* Allow this group to grow and shrink, increased gap */}
                    <img
                        src="/logo.png"
                        alt="Shakti Margam Logo"
                        className="h-10 w-10 sm:h-12 sm:w-12 object-contain flex-shrink-0"
                    />
                    <div className="truncate flex-1 min-w-0">
                        <h1 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-extrabold text-teal-700 leading-tight truncate">Shakti Margam</h1>
                        <p className="text-xs sm:text-sm md:text-base text-gray-500 font-semibold leading-normal truncate">Empowering Women Entrepreneurs</p> {/* Adjusted leading */}
                    </div>
                </div>

                <div className="flex items-center gap-x-1 sm:gap-x-2 md:gap-x-3 flex-shrink-0 ml-4 sm:ml-6"> {/* Increased ml */}
                    <nav className="hidden md:flex items-baseline space-x-2 lg:space-x-4">
                        <Link to="/" className="text-gray-700 hover:text-teal-600 font-semibold text-xs lg:text-sm whitespace-nowrap">Home</Link>
                        <a href="javascript:void(0)" onClick={() => window.redirectToAssistant()} className="text-gray-700 hover:text-teal-600 font-semibold text-xs lg:text-sm whitespace-nowrap">Assistant</a>
                        <Link to="/resources" className="text-gray-700 hover:text-teal-600 font-semibold text-xs lg:text-sm whitespace-nowrap">Resources</Link>
                        <Link to="/about" className="text-gray-700 hover:text-teal-600 font-semibold text-xs lg:text-sm whitespace-nowrap">About</Link>
                    </nav>
                    <button
                        onClick={() => window.redirectToAssistant()}
                        aria-label="Get Started"
                        className="px-2.5 py-1 text-[10px] sm:px-3 sm:py-1.5 sm:text-xs md:px-4 md:py-2 md:text-sm font-semibold bg-primary-accent text-primary-accent-contrast rounded-md sm:rounded-lg shadow-md hover-bg-primary-accent-dark transition-colors duration-300 whitespace-nowrap"
                    >
                        Get Started
                    </button>
                </div>
            </div>
        </div>
      </header>

      <main className="flex-grow w-full"> 
        <section className="relative overflow-hidden bg-hero-teal dark-hero-pochampally-overlay min-h-[calc(100vh-4rem)] flex items-center">
          <div className="container mx-auto px-6">
            <div className="flex flex-col md:flex-row items-center justify-between gap-12 md:gap-16">
              <div className="md:w-1/2 w-full text-center md:text-left mb-12 md:mb-0 flex flex-col items-center md:items-start gap-8">
                <span className="inline-block px-5 py-2 bg-teal-600 text-white rounded-full text-lg font-bold mb-4">
                  AI Assistant for Women Entrepreneurs
                </span>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold text-hero-white mb-6 leading-tight">
                  The Path of <span className="text-amber-400">Empowerment</span>
                </h1>
                <p className="text-xl md:text-2xl text-hero-light mb-8 max-w-xl mx-auto md:mx-0 font-semibold leading-relaxed">
                  Shakti Margam empowers women entrepreneurs in Telangana with personalized AI guidance for business growth, social media strategy, financial planning, and more.
                </p>
                <div className="flex flex-col sm:flex-row justify-center md:justify-start space-y-4 sm:space-y-0 sm:space-x-6 w-full md:w-auto">
                  <button
                    onClick={() => window.redirectToAssistant()}
                    aria-label="Get Started"
                    className="text-center px-8 py-3 text-lg font-semibold bg-primary-accent text-primary-accent-contrast rounded-lg shadow-md hover-bg-primary-accent-dark transition-colors duration-300"
                  >
                    Get Started
                  </button>
                  <button
                    onClick={() => { document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' }); }}
                    aria-label="Explore Features"
                    className="text-center px-8 py-3 text-lg font-semibold text-primary-accent border-2 border-primary-accent rounded-lg hover:bg-primary-accent hover:text-primary-accent-contrast transition-colors duration-300"
                  >
                    Explore Features
                  </button>
                </div>
                <p className="mt-6 text-gray-400 text-lg font-semibold">
                  Trusted by 5,000+ women entrepreneurs in Telangana
                </p>
              </div>
              <div className="md:w-1/2 w-full flex justify-center items-start md:pt-8">
                <div className="relative float-animation" style={{ minWidth: 260, maxWidth: 280 }}>
                  <div className="bg-hero-teal rounded-3xl p-3 shadow-2xl max-w-xs w-full">
                    <div className="bg-white rounded-2xl overflow-hidden">
                      <div className="bg-hero-teal p-3 text-white flex items-center justify-between">
                        <div className="flex items-center">
                          <img src="/logo.png" alt="Shakti Margam Logo" className="h-6 w-6 object-contain mr-2" />
                          <span className="font-bold text-base">SHAKTI MARGAM</span>
                        </div>
                      </div>
                      <div className="p-3 bg-gray-100 h-80 flex flex-col justify-end gap-2 overflow-y-auto chat-messages-container-selector"> 
                         <div className="mb-2">
                          <div className="bg-amber-100 text-slate-800 p-2 rounded-lg rounded-tl-none max-w-xs ml-auto text-sm"> 
                            <p>Hi, I need help with my business strategy.</p>
                          </div>
                        </div>
                        <div className="mb-2">
                          <div className="bg-teal-50 text-slate-800 p-2 rounded-lg rounded-tr-none max-w-xs text-sm">
                            <p>Consider focusing on social media to promote — try sharing content, engaging with followers, and leveraging posts to build an audience online.</p>
                          </div>
                        </div>
                        <div className="mb-2">
                          <div className="bg-teal-50 text-slate-800 p-2 rounded-lg rounded-tr-none max-w-xs text-sm">
                            <p>We should focus on strategies that help boost a fast-growing strategy. To grow, share your engagement—</p>
                          </div>
                        </div>
                        <div className="mt-auto pt-2">
                          <div className="flex items-center bg-white rounded-full border border-gray-300 p-1">
                            <input type="text" id="demo-chat-input" placeholder="Message" className="flex-grow px-3 py-1 bg-transparent focus:outline-none text-sm" />
                            <button onClick={() => window.sendDemoMessage()} className="bg-primary-accent text-primary-accent-contrast p-1.5 rounded-full hover-bg-primary-accent-dark transition-colors"> 
                              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section id="features" className="py-12 md:py-20 bg-white w-full">
          <div className="container mx-auto px-6">
            <div className="text-center mb-12 md:mb-16">
              <span className="inline-block px-3 py-1 pochampally-bg-primary bg-opacity-10 pochampally-text-primary rounded-full text-sm font-medium mb-3 md:mb-4">
                Comprehensive Suite of Tools
              </span>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-3 md:mb-4">
                Your AI-Powered Business Companion
              </h2>
              <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
                Shakti Margam provides personalized guidance across all aspects of your business journey, from startup to growth stage.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12 auto-rows-fr"> {/* Added auto-rows-fr for equal height cards */}
              {/* Feature 1 */}
              <div className="pochampally-bg-accent2-light rounded-lg p-4 md:p-6 shadow-md feature-card cursor-pointer" onClick={() => window.redirectToAssistantWithModule('initial-assessment')}>
                <div className="pochampally-bg-accent2 w-12 h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center mb-4 self-start flex-shrink-0">
                  <svg className="h-6 w-6 md:h-7 md:w-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                    <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd"></path>
                  </svg>
                </div>
                <div className="flex-grow">
                  <h3 className="text-lg md:text-xl font-semibold pochampally-text-accent2-darker mb-2">Initial Business Assessment</h3>
                  <p className="text-sm md:text-base text-gray-700">
                    Analyze your business idea, model, and current performance to identify strengths and areas for improvement.
                  </p>
                </div>
              </div>

              {/* Feature 2 */}
              <div className="pochampally-bg-accent1-light rounded-lg p-4 md:p-6 shadow-md feature-card cursor-pointer" onClick={() => window.redirectToAssistantWithModule('telangana-market-insights')}>
                <div className="pochampally-bg-accent1 w-12 h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center mb-4 self-start flex-shrink-0">
                  <svg className="h-6 w-6 md:h-7 md:w-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                    <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                  </svg>
                </div>
                <div className="flex-grow">
                  <h3 className="text-lg md:text-xl font-semibold pochampally-text-accent1-darker mb-2">Market Intelligence</h3>
                  <p className="text-sm md:text-base text-gray-700">
                    Get data on market size, trends, competitors, and opportunities specific to Telangana.
                  </p>
                </div>
              </div>

              {/* Feature 3 */}
              <div className="pochampally-bg-accent3-light rounded-lg p-4 md:p-6 shadow-md feature-card cursor-pointer" onClick={() => window.redirectToAssistantWithModule('social-media-strategy')}>
                <div className="pochampally-bg-accent3 w-12 h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center mb-4 self-start flex-shrink-0">
                  <svg className="h-6 w-6 md:h-7 md:w-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"></path>
                  </svg>
                </div>
                <div className="flex-grow">
                  <h3 className="text-lg md:text-xl font-semibold pochampally-text-accent3-darker mb-2">Social Media Strategy</h3>
                  <p className="text-sm md:text-base text-gray-700">
                    Create customized social media plans for different platforms popular in Telangana.
                  </p>
                </div>
              </div>

              {/* Feature 4 */}
              <div className="pochampally-bg-accent4-light rounded-lg p-4 md:p-6 shadow-md feature-card cursor-pointer" onClick={() => window.redirectToAssistantWithModule('financial-analysis')}>
                <div className="pochampally-bg-accent4 w-12 h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center mb-4 self-start flex-shrink-0">
                  <svg className="h-6 w-6 md:h-7 md:w-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"></path>
                  </svg>
                </div>
                <div className="flex-grow">
                  <h3 className="text-lg md:text-xl font-semibold pochampally-text-accent4-darker mb-2">Financial Advisor</h3>
                  <p className="text-sm md:text-base text-gray-700">
                    Guidance on cash flow management, funding opportunities for women entrepreneurs, and financial planning.
                  </p>
                </div>
              </div>
              
              {/* Feature 5 */}
              <div className="pochampally-bg-primary-light rounded-lg p-4 md:p-6 shadow-md feature-card cursor-pointer" onClick={() => window.redirectToAssistantWithModule('customer-profiling')}>
                <div className="pochampally-bg-primary w-12 h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center mb-4 self-start flex-shrink-0">
                  <svg className="h-6 w-6 md:h-7 md:w-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                     <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path>
                  </svg>
                </div>
                <div className="flex-grow">
                  <h3 className="text-lg md:text-xl font-semibold pochampally-text-primary mb-2">Customer Profiling</h3>
                  <p className="text-sm md:text-base text-gray-700">
                    Develop detailed customer personas based on Telangana's demographic data.
                  </p>
                </div>
              </div>

               {/* Feature 6 */}
              <div className="pochampally-bg-accent2-light rounded-lg p-4 md:p-6 shadow-md feature-card cursor-pointer" onClick={() => window.redirectToAssistantWithModule('telangana-market-insights')}>
                <div className="pochampally-bg-accent2 w-12 h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center mb-4 self-start flex-shrink-0">
                  <svg className="h-6 w-6 md:h-7 md:w-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"></path>
                  </svg>
                </div>
                <div className="flex-grow">
                  <h3 className="text-lg md:text-xl font-semibold pochampally-text-accent2-darker mb-2">Regional Festival Calendar</h3>
                  <p className="text-sm md:text-base text-gray-700">
                    Integrate Telangana festival calendar with your business planning for seasonal promotions.
                  </p>
                </div>
              </div>

              {/* Feature 7 */}
              <div className="pochampally-bg-accent1-light rounded-lg p-4 md:p-6 shadow-md feature-card cursor-pointer" onClick={() => window.redirectToAssistantWithModule('telangana-market-insights')}>
                 <div className="pochampally-bg-accent1 w-12 h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center mb-4 self-start flex-shrink-0">
                  <svg className="h-6 w-6 md:h-7 md:w-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" clipRule="evenodd"></path>
                  </svg>
                </div>
                <div className="flex-grow">
                  <h3 className="text-lg md:text-xl font-semibold pochampally-text-accent1-darker mb-2">Women Entrepreneur Network</h3>
                  <p className="text-sm md:text-base text-gray-700">
                    Connect with other women entrepreneurs in similar industries across Telangana.
                  </p>
                </div>
              </div>

              {/* Feature 8 */}
              <div className="pochampally-bg-accent3-light rounded-lg p-4 md:p-6 shadow-md feature-card cursor-pointer" onClick={() => window.redirectToAssistantWithModule('telangana-market-insights')}>
                <div className="pochampally-bg-accent3 w-12 h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center mb-4 self-start flex-shrink-0">
                  <svg className="h-6 w-6 md:h-7 md:w-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd"></path>
                  </svg>
                </div>
                <div className="flex-grow">
                  <h3 className="text-lg md:text-xl font-semibold pochampally-text-accent3-darker mb-2">Government Scheme Navigator</h3>
                  <p className="text-sm md:text-base text-gray-700">
                    Identify government schemes and programs you're eligible for with guidance on application processes.
                  </p>
                </div>
              </div>
            </div>

            <div className="text-center mt-12 md:mt-16">
              <button
                onClick={() => window.redirectToAssistant()}
                aria-label="Explore All Features"
                className="inline-block px-8 py-3 text-lg font-semibold pochampally-bg-primary text-white rounded-lg shadow-md hover-pochampally-bg-primary-dark transition-colors duration-300"
              >
                Explore All Features
              </button>
            </div>
          </div>
        </section>

        <section id="success-stories" className="py-16 md:py-20 bg-gray-50 w-full relative overflow-hidden"> {/* Added relative and overflow-hidden */}
          <div className="container mx-auto px-6">
            <div className="text-center mb-12 md:mb-16">
              <span className="inline-block px-3 py-1 pochampally-bg-primary bg-opacity-10 pochampally-text-primary rounded-full text-sm font-medium mb-3 md:mb-4">
                Success Stories
              </span>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-3 md:mb-4">
                Hear from Women Entrepreneurs in Telangana
              </h2>
              <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
                Real success stories from women who transformed their businesses with Shakti Margam's guidance.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white rounded-lg p-6 shadow-md flex flex-col h-full"> {/* Added h-full */}
                <div className="flex items-center mb-4">
                  <div className="h-16 w-16 rounded-full pochampally-bg-accent1-light flex items-center justify-center mr-4 flex-shrink-0"> 
                    <svg className="h-8 w-8 pochampally-text-accent1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Lakshmi R.</h3>
                    <p className="text-gray-600">Handloom Business, Hyderabad</p>
                  </div>
                </div>
                <div className="mb-4">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <svg key={i} className="h-5 w-5 pochampally-text-accent3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                      </svg>
                    ))}
                  </div>
                </div>
                <p className="text-gray-700 italic mb-4 text-sm flex-grow"> {/* Added flex-grow */}
                  "Shakti Margam helped me identify the perfect social media strategy for my handloom business. My online sales increased by 45% in just three months!"
                </p>
                <div className="text-xs text-gray-500 mt-auto"> Using Shakti Margam since 2022 </div>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-md flex flex-col h-full"> {/* Added h-full */}
                <div className="flex items-center mb-4">
                  <div className="h-16 w-16 rounded-full pochampally-bg-accent2-light flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="h-8 w-8 pochampally-text-accent2" fill="currentColor" viewBox="0 0 20 20">
                       <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Priya K.</h3>
                    <p className="text-gray-600">Natural Skincare, Warangal</p>
                  </div>
                </div>
                <div className="mb-4"><div className="flex">{[...Array(5)].map((_, i) => (<svg key={i} className="h-5 w-5 pochampally-text-accent3" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>))}</div></div>
                <p className="text-gray-700 italic mb-4 text-sm flex-grow"> {/* Added flex-grow */}
                    "The financial forecasting feature saved my business..."
                </p>
                <div className="text-xs text-gray-500 mt-auto">Using Shakti Margam since 2021</div>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-md flex flex-col h-full"> {/* Added h-full */}
                <div className="flex items-center mb-4">
                  <div className="h-16 w-16 rounded-full pochampally-bg-accent3-light flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="h-8 w-8 pochampally-text-accent3" fill="currentColor" viewBox="0 0 20 20">
                       <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Sunitha M.</h3>
                    <p className="text-gray-600">Organic Food Startup, Nizamabad</p>
                  </div>
                </div>
                <div className="mb-4"><div className="flex">{[...Array(5)].map((_, i) => (<svg key={i} className="h-5 w-5 pochampally-text-accent3" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>))}</div></div>
                <p className="text-gray-700 italic mb-4 text-sm flex-grow"> {/* Added flex-grow */}
                    "As a first-time entrepreneur, I had so many questions..."
                </p>
                <div className="text-xs text-gray-500 mt-auto">Using Shakti Margam since 2023</div>
              </div>
            </div>
            <div className="text-center mt-12 md:mt-16">
              <a href="#" className="inline-block px-6 py-3 bg-white pochampally-text-primary pochampally-border-primary border font-medium rounded-lg hover-pochampally-bg-primary-light transition-colors duration-300 text-base">
                Read More Success Stories
              </a>
            </div>
          </div>
        </section>

        <section id="get-started" className="py-16 md:py-20 pochampally-bg-gradient-cta w-full relative overflow-hidden">
          <div className="container mx-auto px-6">
            <div className="bg-white rounded-xl shadow-xl overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2">
                <div className="p-8 md:p-12">
                  <h2 className="text-2xl md:text-3xl font-bold pochampally-text-primary mb-4">
                    Ready to Transform Your Business Journey?
                  </h2>
                  <p className="text-gray-600 mb-8 text-base md:text-lg">
                    Join thousands of women entrepreneurs in Telangana who are achieving their business goals with Shakti Margam's personalized AI guidance.
                  </p>
                  <div className="space-y-4">
                    <div className="flex items-start gap-6 h-full"><svg className="h-6 w-6 pochampally-text-accent2 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path></svg><div><h3 className="font-semibold text-gray-900 text-base md:text-lg">Free to Get Started</h3><p className="text-gray-600 text-sm">No credit card required.</p></div></div>
                    <div className="flex items-start gap-6 h-full"><svg className="h-6 w-6 pochampally-text-accent2 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path></svg><div><h3 className="font-semibold text-gray-900 text-base md:text-lg">WE-HUB Partner</h3><p className="text-gray-600 text-sm">Official partner of WE-HUB.</p></div></div>
                    <div className="flex items-start gap-6 h-full"><svg className="h-6 w-6 pochampally-text-accent2 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path></svg><div><h3 className="font-semibold text-gray-900 text-base md:text-lg">Trusted by 5,000+ Users</h3><p className="text-gray-600 text-sm">Join a growing community.</p></div></div>
                  </div>
                  <div className="mt-8">
                     <button
                        onClick={() => window.redirectToAssistant()}
                        aria-label="Get Started Free"
                        className="inline-block px-8 py-3 text-lg font-semibold pochampally-bg-primary text-white rounded-lg shadow-md hover-pochampally-bg-primary-dark transition-colors duration-300"
                      >
                        Get Started Free
                    </button>
                    <p className="text-sm text-gray-500 mt-2">No credit card required</p>
                    <div className="mt-8 border-t border-gray-200 pt-6">
                      <h3 className="text-lg font-semibold mb-3 text-slate-800">Subscribe to our Newsletter</h3>
                      <p className="text-gray-600 mb-4 text-sm">Get the latest updates and resources.</p>
                      <form onSubmit={(e) => window.handleFormSubmit(e, 'newsletter')}>
                        <div className="flex">
                          <input type="email" name="email" placeholder="Your email address" required
                            className="flex-grow px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" />
                          <button type="submit"
                            className="pochampally-bg-primary text-white px-4 py-2 rounded-r-lg hover-pochampally-bg-primary-dark transition-colors duration-300 text-sm">
                            Subscribe
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
                <div className="pochampally-bg-primary-light p-8 md:p-12 flex items-center justify-center">
                  <div className="text-center">
                    <div className="mb-6">
                      <img src="/logo.png" alt="Shakti Margam Logo" className="h-16 mx-auto" />
                    </div>
                    <blockquote className="text-lg md:text-xl italic text-gray-700 mb-6">
                      "Shakti Margam has been instrumental in helping women entrepreneurs across Telangana realize their business potential."
                    </blockquote>
                    <div className="flex items-center justify-center">
                      <div className="h-12 w-12 rounded-full bg-white flex items-center justify-center mr-3 flex-shrink-0">
                        <svg className="h-6 w-6 pochampally-text-primary" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path>
                        </svg>
                      </div>
                      <div className="text-left">
                        <h4 className="font-semibold text-base md:text-lg text-gray-800">Sita Pallacholla</h4>
                        <p className="text-sm text-gray-600">Director, WE-HUB Telangana</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <footer className="bg-slate-800 text-white py-12 w-full">
        <div className="container mx-auto px-6">
          <div className="flex flex-col md:flex-row justify-between gap-8">
            <div className="mb-8 md:mb-0 md:w-1/3">
              <img src="/logo.png" alt="Shakti Margam Logo" className="h-12 mb-4" />
              <p className="text-gray-400 text-sm leading-relaxed">
                AI-powered assistant platform for women entrepreneurs in Telangana, providing personalized guidance for business growth.
              </p>
              <div className="flex space-x-4 mt-4">
                <a href="#" className="text-gray-400 hover:text-white"><svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-label="Facebook"><path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"></path></svg></a>
                <a href="#" className="text-gray-400 hover:text-white"><svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-label="LinkedIn"><path d="M12 2C6.477 2 2 6.477 2 12c0 5.523 4.477 10 10 10 5.523 0 10-4.477 10-10 0-5.523-4.477-10-10-10zm2.99 15h-2v-6h2v6zm-1-6.93c-.7 0-1.27-.57-1.27-1.27 0-.7.57-1.27 1.27-1.27.7 0 1.27.57 1.27 1.27 0 .7-.57 1.27-1.27 1.27zm4.01 6.93h-2v-3.5c0-.79-.18-1.5-1.25-1.5s-1.25.71-1.25 1.5V17h-2v-6h2v.92c.38-.35.86-.92 1.82-.92 1.94 0 2.68 1.28 2.68 2.94V17z"></path></svg></a>
                <a href="#" className="text-gray-400 hover:text-white"><svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-label="Twitter"><path d="M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z"></path></svg></a>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-8 sm:grid-cols-2 md:w-2/3 text-sm">
              <div>
                <h3 className="text-base font-semibold text-gray-200 mb-4">Quick Links</h3>
                <ul className="space-y-2">
                  <li><Link to="/" className="text-gray-400 hover:text-white transition-colors">Home</Link></li>
                  <li><a href="javascript:void(0)" onClick={() => window.redirectToAssistant()} className="text-gray-400 hover:text-white transition-colors">Assistant</a></li>
                  <li><Link to="/about" className="text-gray-400 hover:text-white transition-colors">About Us</Link></li>
                  <li><a href="#features" onClick={(e) => { e.preventDefault(); document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });}} className="text-gray-400 hover:text-white transition-colors">Features</a></li>
                  <li><a href="#success-stories" onClick={(e) => { e.preventDefault(); document.getElementById('success-stories')?.scrollIntoView({ behavior: 'smooth' });}} className="text-gray-400 hover:text-white transition-colors">Success Stories</a></li>
                  <li><a href="#get-started" onClick={(e) => { e.preventDefault(); document.getElementById('get-started')?.scrollIntoView({ behavior: 'smooth' });}} className="text-gray-400 hover:text-white transition-colors">Get Started</a></li>
                </ul>
              </div>
              <div>
                <h3 className="text-base font-semibold text-gray-200 mb-4">Resources</h3>
                <ul className="space-y-2">
                  <li><Link to="/resources" className="text-gray-400 hover:text-white transition-colors">All Resources</Link></li>
                  <li><a href="https://wehub.telangana.gov.in/" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">WE-HUB</a></li>
                  <li><Link to="/resources#government-schemes" className="text-gray-400 hover:text-white transition-colors">Government Schemes</Link></li>
                  <li><Link to="/resources#financial-support" className="text-gray-400 hover:text-white transition-colors">Financial Support</Link></li>
                  <li><Link to="/resources#market-insights" className="text-gray-400 hover:text-white transition-colors">Market Insights</Link></li>
                  <li><Link to="/resources#business-templates" className="text-gray-400 hover:text-white transition-colors">Business Templates</Link></li>
                </ul>
              </div>
            </div>
          </div>
          <div className="mt-12 pt-8 border-t border-gray-700 text-center text-gray-400 text-xs">
            <p>© 2025 Shakti Margam. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

declare global {
  interface Window {
    redirectToAssistant: () => void;
    redirectToAssistantWithModule: (moduleId: string) => void;
    handleFormSubmit: (event: any, formType: string) => boolean;
    showSuccessMessage: (form: HTMLFormElement) => void;
    sendDemoMessage: () => void;
    API_ENDPOINT?: string;
  }
}

export default StaticLandingPage;
