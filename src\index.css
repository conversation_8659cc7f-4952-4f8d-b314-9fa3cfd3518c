/* Import animations - must come before Tailwind directives */
@import './styles/animations.css'; /* This should ideally be imported in main.js/ts or App.js/tsx before any other CSS */

/* Base styles */
:root {
  --header-height: 4rem; /* Example variable, ensure it's used or remove */
}

/* Responsive typography - Base font size for rem calculation */
html {
  font-size: 16px; /* Standard base */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* body { */
  /* font-family defined in custom.css, ensure only one source of truth or consistent definition */
/* } */


/* Layout fixes */
.feature-card {
  height: 100%; /* Good for grid items to take equal height */
  display: flex;
  flex-direction: column;
}

/* SVG sizing within feature cards - provides a fallback */
/* Tailwind classes on the SVG element itself (e.g., h-6 w-6) should ideally take precedence */
.feature-card svg {
  max-width: 28px;  /* <PERSON>lightly increased default max from 24px */
  max-height: 28px; /* <PERSON>lightly increased default max from 24px */
  display: inline-block; /* Ensure proper alignment */
}

/* Image constraints */
img {
  max-width: 100%;
  height: auto;
  display: block; /* Common reset for images */
}

/* Icon size constraints for a generic icon-container class (if used) */
/* .icon-container {
  width: 48px;
  height: 48px;
}

@media (min-width: 768px) {
  .icon-container {
    width: 56px;
    height: 56px;
  }
} */

/* Fix container padding - Generally, prefer Tailwind's max-w- utils and px- on sections */
/* .container {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
} */

/* Ensure proper text contrast - Tailwind provides text color utilities */
/* .text-gray-600 {
  color: #4B5563; 
} */

/* Fix button sizing - Prefer Tailwind classes for buttons */
/* .btn {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

@media (min-width: 768px) {
  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 1rem;
    line-height: 1.5rem;
  }
} */
