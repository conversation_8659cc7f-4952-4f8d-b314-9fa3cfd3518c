# Shakti Margam Sample Data Guide

This document provides information about the sample data included in the Shakti Margam application for demonstration purposes.

## Sample User Accounts

### Admin User
- **Email:** <EMAIL>
- **Password:** Sanjureddy@007
- **Role:** Administrator
- **Access:** Full access to all features including admin dashboard

### Demo User
- **Email:** <EMAIL>
- **Password:** Demo@123
- **Role:** Regular user
- **Access:** Access to all user features

### Guest Mode
- **Access:** Limited access to essential features
- **Note:** No login required, data is stored in local storage only

## Sample Business Profiles

### Handloom Business
- **Name:** Pochampally Handlooms
- **Location:** Warangal, Telangana
- **Industry:** Textiles
- **Stage:** Growth
- **Description:** Traditional handloom business specializing in Pochampally Ikat sarees and fabrics

### Organic Food Startup
- **Name:** Telangana Organics
- **Location:** Hyderabad, Telangana
- **Industry:** Food & Beverage
- **Stage:** Early
- **Description:** Organic food startup focusing on locally sourced produce and traditional recipes

### Handicraft Export Business
- **Name:** Nizamabad Crafts
- **Location:** Nizamabad, Telangana
- **Industry:** Handicrafts
- **Stage:** Established
- **Description:** Export-oriented business selling traditional Telangana handicrafts to international markets

### Tech Education Service
- **Name:** TechSkills Telangana
- **Location:** Karimnagar, Telangana
- **Industry:** Education
- **Stage:** Growth
- **Description:** Technology education service providing training to women in rural Telangana

## Sample Queries for Demonstration

### Initial Assessment Module
- "I want to start a handloom business in Warangal. Can you help me assess the viability?"
- "What are the key factors I should consider for my organic food startup in Hyderabad?"
- "How can I evaluate the export potential for my handicraft business in Nizamabad?"
- "What are the main challenges for a tech education service in Karimnagar?"

### Social Media Strategy Module
- "I need a social media strategy for my handloom business targeting urban customers."
- "How can I use Instagram to promote my organic food products?"
- "What content should I create for my handicraft export business on LinkedIn?"
- "How often should I post on social media for my tech education service?"

### Financial Analysis Module
- "How can I manage cash flow for my growing handloom business?"
- "What financial metrics should I track for my organic food startup?"
- "How should I price my handicrafts for export markets?"
- "What are the typical startup costs for a tech education service in Telangana?"

### Telangana Market Insights Module
- "What are the major markets for handloom products in Telangana?"
- "Who are the main competitors in the organic food space in Hyderabad?"
- "What are the export regulations for handicrafts from Telangana?"
- "What is the demand for tech education in rural Telangana?"

### Customer Profiling Module
- "Who is the ideal customer for Pochampally Ikat sarees?"
- "What are the buying habits of organic food consumers in Hyderabad?"
- "How do international buyers discover and purchase Indian handicrafts?"
- "What demographics should I target for my tech education service in Karimnagar?"

## Sample Data Files

The following sample data files are included in the application:

### Market Data
- `src/data/market-data/telangana-market-overview.json`: Overview of key markets in Telangana
- `src/data/telangana-market-insights.ts`: Detailed market insights for various industries

### Government Schemes
- `src/data/government-schemes/telangana-women-entrepreneur-schemes.json`: Information about government schemes for women entrepreneurs
- `src/data/telangana-funding.ts`: Funding opportunities available in Telangana

### Business Guides
- `src/data/business-guides/social-media-strategy-guide.json`: Guide for social media strategy
- `src/data/telangana-resources-integration.ts`: Resources for business integration

### Success Stories
- `src/data/success-stories/telangana-women-entrepreneurs.json`: Success stories of women entrepreneurs in Telangana

## Using Sample Data for Demonstration

When demonstrating the application:

1. **Choose a Business Profile:** Select one of the sample business profiles to provide context
2. **Select a Module:** Navigate to the appropriate module based on the demonstration focus
3. **Use Sample Queries:** Use the provided sample queries to demonstrate the AI's capabilities
4. **Highlight Telangana-Specific Information:** Emphasize how the responses include Telangana-specific data
5. **Show Structured Responses:** Demonstrate how the AI provides structured, actionable information

## Customizing Sample Data

To customize the sample data for specific demonstration needs:

1. Edit the corresponding JSON files in the `src/data` directory
2. Update the TypeScript files with additional data points
3. Restart the application to load the updated data

## Data Limitations

Note that the sample data is for demonstration purposes only and has the following limitations:

1. Limited scope and depth compared to a production system
2. May not reflect the most current market conditions
3. Simplified for demonstration clarity
4. Not connected to real-time data sources

For a production deployment, the application would be connected to comprehensive data sources and regularly updated information.
